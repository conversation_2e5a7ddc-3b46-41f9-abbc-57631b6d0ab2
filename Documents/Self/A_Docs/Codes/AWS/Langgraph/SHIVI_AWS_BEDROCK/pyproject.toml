[project]
name = "deep_research_from_scratch"
version = "0.1.0"
description = "Build a deep research agent from scratch"
requires-python = ">=3.11"
dependencies = [
    "langgraph>=0.5.4",
    "langchain>=0.3.0",
    "langchain-openai>=0.2.0",
    "langchain-anthropic>=0.3.0",
    "langchain_community>=0.3.27",
    "langchain_tavily>=0.2.7",
    "langchain_mcp_adapters>=0.1.9",
    "pydantic>=2.0.0",
    "rich>=14.0.0",
    "jupyter>=1.0.0",
    "ipykernel>=6.20.0",
    "tavily-python>=0.5.0",
    "langchain[aws]",
    "ipython>=9.4.0",
]

[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1"]

[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["deep_research_from_scratch"]

[tool.setuptools.package-dir]
"deep_research_from_scratch" = "src/deep_research_from_scratch"

[tool.setuptools.package-data]
"*" = ["py.typed"]

[tool.ruff]
lint.select = [
    "E",    # Fixes spacing, indentation
    "F",    # Finds import pandas but you never use pandas or other imports 
    "I",    # isort - sort the imports
    "D",    # pydocstyle, check if codes have documentation
    "D401", # First line should be in imperative mood
    "T201",
    "UP",
]
lint.ignore = [
    "UP006",
    "UP007",
    "UP035",
    "D417",
    "E501",
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D", "UP"]

[tool.ruff.lint.pydocstyle]
convention = "google"
